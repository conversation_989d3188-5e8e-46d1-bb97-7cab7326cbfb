const { sql, queryWithParams } = require('../service/index.js')
const jwt = require('jsonwebtoken')
const crypto = require('crypto')
const xml2js = require('xml2js')
const eviltransform = require('eviltransform')

class CommonController {
  async verify(ctx, next) {
    try {
      const authorization = ctx.headers.authorization
      if (!authorization) return (ctx.body = { code: 200, data: false, msg: '验证失败' })
      const token = authorization.replace('Bearer ', '')
      // 解析token
      const result = jwt.verify(token, process.env.JWT_SECRET, { algorithms: ['HS256'] })
      const id = result['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier']
      const userName = result['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name']
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[User] WHERE id = @id AND Username = @userName`, {
        id: { type: sql.Int, value: id },
        userName: { type: sql.NVarChar, value: userName }
      })
      if (!recordsets[0][0].id) return (ctx.body = { code: 200, data: false, msg: '验证失败' })
      ctx.body = { code: 200, data: true, msg: '查询成功' }
    } catch (error) {
      ctx.body = { code: 200, data: false, msg: '验证失败', error: error.message }
    }
  }

  async wxverify(ctx, next) {
    const method = ctx.request.method
    if (method === 'GET') {
      try {
        // 获取微信服务器发送的参数
        const { signature, timestamp, nonce, echostr } = ctx.request.query

        console.log('微信验证参数:', { signature, timestamp, nonce, echostr })

        // 微信服务号的Token，从您的配置中获取
        const token = 'WXTOKEN' // 这应该与您在微信开发者平台配置的Token一致

        // 验证签名 - 直接在这里实现验证逻辑
        const isValid = CommonController.checkSignature(signature, timestamp, nonce, token)

        if (isValid) {
          console.log('微信签名验证成功')
          // 验证成功，返回echostr
          ctx.body = echostr
        } else {
          console.log('微信签名验证失败')
          ctx.status = 403
          ctx.body = 'Forbidden'
        }
      } catch (error) {
        console.error('微信验证接口错误:', error)
        ctx.status = 500
        ctx.body = 'Internal Server Error'
      }
    } else {
      // 这里处理服务号推送的消息
      try {
        const body = ctx.request.body
        console.log('收到微信消息:', body)

        // 解析XML格式的消息体为JSON
        const parser = new xml2js.Parser({ explicitArray: false, ignoreAttrs: true, trim: true })

        const result = await parser.parseStringPromise(body)
        const message = result.xml

        console.log('解析后的消息:', message)

        // 根据消息类型处理不同的消息
        const msgType = message.MsgType
        const fromUser = message.FromUserName
        const toUser = message.ToUserName

        switch (msgType) {
          case 'text':
            console.log('收到文本消息:', message.Content)
            // 这里可以添加文本消息处理逻辑
            // 回复你好的内容
            const replyMessage = CommonController.createTextReply(fromUser, toUser, '你好！感谢你的消息。')
            ctx.body = replyMessage
            return
          case 'image':
            console.log('收到图片消息:', message.PicUrl)
            // 这里可以添加图片消息处理逻辑
            const imageReply = CommonController.createTextReply(fromUser, toUser, '收到了你的图片，很棒！')
            ctx.body = imageReply
            return
          case 'voice':
            console.log('收到语音消息:', message.MediaId)
            // 这里可以添加语音消息处理逻辑
            const voiceReply = CommonController.createTextReply(fromUser, toUser, '收到了你的语音消息！')
            ctx.body = voiceReply
            return
          case 'event':
            console.log('收到事件消息:', message.Event)
            // 这里可以添加事件消息处理逻辑
            if (message.Event === 'subscribe') {
              const welcomeReply = CommonController.createTextReply(fromUser, toUser, '欢迎关注我们的公众号！')
              ctx.body = welcomeReply
              return
            }
            break
          default:
            console.log('未知消息类型:', msgType)
        }

        // 返回成功响应
        ctx.body = 'success'
      } catch (error) {
        console.error('解析微信消息XML失败:', error)
        ctx.status = 500
        ctx.body = 'Internal Server Error'
      }
    }
  }

  // 创建文本回复消息
  static createTextReply(fromUser, toUser, content) {
    const timestamp = Math.floor(Date.now() / 1000)
    return `<xml>
<ToUserName><![CDATA[${fromUser}]]></ToUserName>
<FromUserName><![CDATA[${toUser}]]></FromUserName>
<CreateTime>${timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[${content}]]></Content>
</xml>`
  }

  // 验证微信签名的方法
  static checkSignature(signature, timestamp, nonce, token) {
    try {
      // 1. 将token、timestamp、nonce三个参数进行字典序排序
      const tmpArr = [token, timestamp, nonce]
      tmpArr.sort()

      // 2. 将三个参数字符串拼接成一个字符串进行sha1加密
      const tmpStr = tmpArr.join('')
      const hashCode = crypto.createHash('sha1').update(tmpStr).digest('hex')

      // 3. 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
      return hashCode === signature
    } catch (error) {
      console.error('签名验证过程出错:', error)
      return false
    }
  }

  async dispose(ctx) {
    const { recordsets } = await queryWithParams(`SELECT PumpRoomNumber,X, Y FROM dbo.[SecondaryWaterProgressNew]`, {})

  }
}

module.exports = new CommonController()
